# 🎾 Tennis Betting Analytics - Kafka Implementation

## 📋 Visão Geral

Esta implementação substitui a leitura de logs por um sink Kafka dedicado para disponibilizar os dados processados pelo PyFlink de forma mais robusta e escalável.

## 🏗️ Arquitetura Atualizada

```
Tennis Simulator → Kafka (tennis-bets) → PyFlink → Kafka (tennis-aggregations) → Dashboard
```

### Componentes:

1. **Tennis Simulator**: Gera eventos de apostas e publica no tópico `tennis-bets`
2. **PyFlink Job**: Processa eventos em janelas de 10 segundos e publica agregações no tópico `tennis-aggregations`
3. **Dashboard**: Consome agregações do tópico `tennis-aggregations` em tempo real

## 🔧 Principais Mudanças

### 1. PyFlink Job (`flink/tennis_streaming_job.py`)
- ✅ Adicionado sink Kafka para publicar agregações
- ✅ Nova classe `AggregationToJsonMapper` para serialização JSON
- ✅ Configuração do tópico `tennis-aggregations`
- ✅ Mantida compatibilidade com logs para debug

### 2. Dashboard (`dashboard/tennis_dashboard.py`)
- ✅ Substituída classe `TennisDataCollector` por `KafkaAggregationConsumer`
- ✅ Implementado consumidor Kafka em thread separada
- ✅ Adicionado fallback para diferentes servidores Kafka
- ✅ Melhor tratamento de erros e reconexão

### 3. Docker Compose (`docker-compose.yml`)
- ✅ Adicionado serviço `kafka-setup` para criação automática de tópicos
- ✅ Configuradas dependências corretas entre serviços
- ✅ Garantia de que tópicos existem antes do Flink iniciar

### 4. Dependências
- ✅ Adicionado `kafka-python==2.0.2` ao dashboard

## 🚀 Como Usar

### 1. Iniciar o Sistema
```bash
# Parar containers existentes
docker-compose down

# Reconstruir e iniciar
docker-compose up --build -d

# Verificar logs
docker-compose logs -f
```

### 2. Verificar Tópicos Kafka
```bash
# Executar script de teste
./scripts/test-kafka-topics.sh

# Ou manualmente
docker exec kafka kafka-topics --bootstrap-server localhost:29092 --list
```

### 3. Testar Consumidor Kafka
```bash
# Executar teste Python
python scripts/test-kafka-consumer.py
```

### 4. Acessar Dashboard
- URL: http://localhost:8501
- O dashboard agora consome dados diretamente do Kafka

## 🔍 Monitoramento

### Verificar Mensagens no Tópico
```bash
# Consumir mensagens do tópico de agregações
docker exec kafka kafka-console-consumer \
  --bootstrap-server localhost:29092 \
  --topic tennis-aggregations \
  --from-beginning
```

### Logs dos Serviços
```bash
# Flink TaskManager
docker-compose logs -f flink-taskmanager

# Dashboard
docker-compose logs -f tennis-dashboard

# Kafka
docker-compose logs -f kafka
```

## 🛠️ Troubleshooting

### Dashboard não recebe dados
1. Verificar se o job Flink está rodando:
   ```bash
   docker-compose logs flink-taskmanager
   ```

2. Verificar se mensagens estão sendo publicadas:
   ```bash
   docker exec kafka kafka-console-consumer --bootstrap-server localhost:29092 --topic tennis-aggregations --from-beginning
   ```

3. Verificar conectividade Kafka:
   ```bash
   python scripts/test-kafka-consumer.py
   ```

### Tópicos não criados
```bash
# Recriar tópicos manualmente
docker exec kafka kafka-topics --create --if-not-exists --bootstrap-server localhost:29092 --partitions 1 --replication-factor 1 --topic tennis-bets
docker exec kafka kafka-topics --create --if-not-exists --bootstrap-server localhost:29092 --partitions 1 --replication-factor 1 --topic tennis-aggregations
```

## 📊 Vantagens da Nova Implementação

1. **Robustez**: Dados persistidos no Kafka, não dependem de logs
2. **Escalabilidade**: Múltiplos consumidores podem processar os dados
3. **Resiliência**: Reconexão automática em caso de falhas
4. **Flexibilidade**: Fácil adição de novos consumidores
5. **Debugging**: Logs ainda disponíveis para debug

## 🔄 Compatibilidade

- ✅ Mantida compatibilidade com logs para debug
- ✅ Dashboard funciona com ambas as implementações
- ✅ Estrutura de dados permanece a mesma
- ✅ Scripts existentes continuam funcionando
