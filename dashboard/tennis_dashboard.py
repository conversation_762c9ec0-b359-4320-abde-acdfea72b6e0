#!/usr/bin/env python3

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import time
from datetime import datetime, timedelta
import json
from collections import deque
import threading
import queue
from kafka import KafkaConsumer
from kafka.errors import KafkaError

# Configure Streamlit page
st.set_page_config(
    page_title="🎾 Tennis Betting Analytics",
    page_icon="🎾",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #ff6b6b;
    }
    .stMetric > label {
        font-size: 14px !important;
    }
    .stMetric > div {
        font-size: 24px !important;
    }
</style>
""", unsafe_allow_html=True)

class KafkaAggregationConsumer:
    def __init__(self, bootstrap_servers='kafka:29092', topic='tennis-aggregations'):
        self.bootstrap_servers = bootstrap_servers
        self.topic = topic
        self.data_queue = queue.Queue()
        self.aggregations = deque(maxlen=100)  # Keep last 100 aggregations
        self.running = False
        self.consumer = None

    def parse_kafka_message(self, message):
        """Parse Kafka message to extract aggregation data"""
        try:
            data = json.loads(message.value.decode('utf-8'))
            return {
                'timestamp': datetime.fromisoformat(data['window_end'].replace(' ', 'T')),
                'outcome': data['outcome'],
                'count': int(data['count']),
                'amount': float(data['total_amount']),
                'received_at': datetime.now()
            }
        except Exception as e:
            st.error(f"Error parsing Kafka message: {e}")
            return None

    def consume_messages(self):
        """Consume messages from Kafka topic"""
        try:
            # Create Kafka consumer
            self.consumer = KafkaConsumer(
                self.topic,
                bootstrap_servers=[self.bootstrap_servers],
                auto_offset_reset='latest',  # Start from latest messages
                enable_auto_commit=True,
                group_id='tennis-dashboard-group',
                value_deserializer=lambda x: x,  # We'll handle JSON parsing manually
                consumer_timeout_ms=1000  # Timeout to allow checking self.running
            )

            st.success(f"✅ Connected to Kafka topic: {self.topic}")

            # Consume messages
            for message in self.consumer:
                if not self.running:
                    break

                data = self.parse_kafka_message(message)
                if data:
                    self.data_queue.put(data)

        except KafkaError as e:
            st.error(f"Kafka error: {e}")
        except Exception as e:
            st.error(f"Error consuming Kafka messages: {e}")
        finally:
            if self.consumer:
                self.consumer.close()

    def start_collection(self):
        """Start Kafka message consumption in background thread"""
        self.running = True
        self.thread = threading.Thread(target=self.consume_messages, daemon=True)
        self.thread.start()

    def stop_collection(self):
        """Stop Kafka message consumption"""
        self.running = False
        if self.consumer:
            self.consumer.close()

    def get_latest_data(self):
        """Get latest aggregation data"""
        # Process queued data
        while not self.data_queue.empty():
            try:
                data = self.data_queue.get_nowait()
                self.aggregations.append(data)
            except queue.Empty:
                break

        return list(self.aggregations)

def create_metrics_cards(df_current):
    """Create KPI metric cards"""
    if df_current.empty:
        st.info("⏳ Aguardando dados...")
        return
    
    col1, col2, col3, col4 = st.columns(4)
    
    total_bets = df_current['count'].sum()
    total_turnover = df_current['amount'].sum()
    avg_bet = total_turnover / total_bets if total_bets > 0 else 0
    outcomes_count = len(df_current)
    
    with col1:
        st.metric(
            label="🎯 Total de Apostas",
            value=f"{total_bets:,}",
            delta=f"Últimos 10s"
        )
    
    with col2:
        st.metric(
            label="💰 Turnover Total",
            value=f"R$ {total_turnover:,.2f}",
            delta=f"Últimos 10s"
        )
    
    with col3:
        st.metric(
            label="📊 Aposta Média",
            value=f"R$ {avg_bet:,.2f}",
            delta=f"Por aposta"
        )
    
    with col4:
        st.metric(
            label="🎾 Outcomes Ativos",
            value=f"{outcomes_count}",
            delta=f"Tipos diferentes"
        )

def create_outcome_charts(df_current):
    """Create outcome analysis charts"""
    if df_current.empty:
        return
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📊 Volume por Outcome")
        fig_count = px.bar(
            df_current, 
            x='outcome', 
            y='count',
            title="Número de Apostas por Outcome",
            color='count',
            color_continuous_scale='viridis'
        )
        fig_count.update_layout(height=400)
        st.plotly_chart(fig_count, use_container_width=True)
    
    with col2:
        st.subheader("💰 Turnover por Outcome")
        fig_amount = px.bar(
            df_current, 
            x='outcome', 
            y='amount',
            title="Turnover por Outcome (R$)",
            color='amount',
            color_continuous_scale='plasma'
        )
        fig_amount.update_layout(height=400)
        st.plotly_chart(fig_amount, use_container_width=True)

def create_timeline_chart(df_timeline):
    """Create timeline chart"""
    if df_timeline.empty:
        return
    
    st.subheader("📈 Timeline de Apostas")
    
    # Group by timestamp and outcome
    timeline_data = df_timeline.groupby(['timestamp', 'outcome']).agg({
        'count': 'sum',
        'amount': 'sum'
    }).reset_index()
    
    fig = make_subplots(
        rows=2, cols=1,
        subplot_titles=('Volume de Apostas', 'Turnover (R$)'),
        vertical_spacing=0.1
    )
    
    outcomes = timeline_data['outcome'].unique()
    colors = px.colors.qualitative.Set3
    
    for i, outcome in enumerate(outcomes):
        outcome_data = timeline_data[timeline_data['outcome'] == outcome]
        color = colors[i % len(colors)]
        
        # Volume chart
        fig.add_trace(
            go.Scatter(
                x=outcome_data['timestamp'],
                y=outcome_data['count'],
                name=f"{outcome} (volume)",
                line=dict(color=color),
                mode='lines+markers'
            ),
            row=1, col=1
        )
        
        # Turnover chart
        fig.add_trace(
            go.Scatter(
                x=outcome_data['timestamp'],
                y=outcome_data['amount'],
                name=f"{outcome} (turnover)",
                line=dict(color=color, dash='dash'),
                mode='lines+markers',
                showlegend=False
            ),
            row=2, col=1
        )
    
    fig.update_layout(height=600, title_text="Evolução Temporal das Apostas")
    st.plotly_chart(fig, use_container_width=True)

def main():
    # Header
    st.title("🎾 Tennis Betting Analytics Dashboard")
    st.markdown("**Monitoramento em Tempo Real das Agregações de Apostas**")
    
    # Initialize Kafka consumer
    if 'consumer' not in st.session_state:
        # Try to connect to Kafka inside Docker network first, fallback to localhost
        kafka_servers = ['kafka:29092', 'localhost:9092']

        for server in kafka_servers:
            try:
                st.session_state.consumer = KafkaAggregationConsumer(bootstrap_servers=server)
                st.session_state.consumer.start_collection()
                st.info(f"🔗 Connected to Kafka at {server}")
                break
            except Exception as e:
                st.warning(f"Failed to connect to {server}: {e}")
                continue
        else:
            st.error("❌ Could not connect to Kafka. Please check if Kafka is running.")
            return
    
    # Sidebar controls
    st.sidebar.header("⚙️ Controles")
    
    auto_refresh = st.sidebar.checkbox("🔄 Auto Refresh", value=True)
    refresh_interval = st.sidebar.slider("Intervalo (segundos)", 1, 10, 3)
    
    time_window = st.sidebar.selectbox(
        "📅 Janela de Tempo",
        ["Últimos 5 min", "Últimos 10 min", "Últimos 30 min", "Última 1 hora"]
    )
    
    outcome_filter = st.sidebar.multiselect(
        "🎯 Filtrar Outcomes",
        ["player1_win", "player2_win", "over_2.5_sets", "under_2.5_sets", 
         "first_set_player1", "first_set_player2"],
        default=[]
    )
    
    # Manual refresh button
    if st.sidebar.button("🔄 Refresh Manual"):
        st.rerun()
    
    # Get latest data
    data = st.session_state.consumer.get_latest_data()

    if not data:
        st.info("⏳ Aguardando dados do Kafka... Verifique se o job Flink está rodando e publicando no tópico 'tennis-aggregations'.")
        st.code("docker-compose logs -f flink-taskmanager")
        return
    
    # Convert to DataFrame
    df = pd.DataFrame(data)
    
    # Apply time filter
    now = datetime.now()
    time_deltas = {
        "Últimos 5 min": timedelta(minutes=5),
        "Últimos 10 min": timedelta(minutes=10),
        "Últimos 30 min": timedelta(minutes=30),
        "Última 1 hora": timedelta(hours=1)
    }
    
    cutoff_time = now - time_deltas[time_window]
    df_filtered = df[df['timestamp'] >= cutoff_time].copy()
    
    # Apply outcome filter
    if outcome_filter:
        df_filtered = df_filtered[df_filtered['outcome'].isin(outcome_filter)]
    
    # Get current window data (last 10 seconds)
    if not df_filtered.empty:
        latest_timestamp = df_filtered['timestamp'].max()
        df_current = df_filtered[df_filtered['timestamp'] == latest_timestamp]
    else:
        df_current = pd.DataFrame()
    
    # Display metrics
    create_metrics_cards(df_current)
    
    st.markdown("---")
    
    # Display charts
    create_outcome_charts(df_current)
    
    st.markdown("---")
    
    # Timeline chart
    create_timeline_chart(df_filtered)
    
    st.markdown("---")
    
    # Data table
    st.subheader("📋 Dados Recentes")
    
    if not df_filtered.empty:
        # Show last 20 records
        df_display = df_filtered.tail(20).copy()
        df_display['timestamp'] = df_display['timestamp'].dt.strftime('%H:%M:%S')
        df_display['amount'] = df_display['amount'].apply(lambda x: f"R$ {x:,.2f}")
        
        st.dataframe(
            df_display[['timestamp', 'outcome', 'count', 'amount']].sort_values('timestamp', ascending=False),
            use_container_width=True
        )
    else:
        st.info("Nenhum dado disponível para o período selecionado.")
    
    # Status info
    st.sidebar.markdown("---")
    st.sidebar.subheader("📊 Status")
    st.sidebar.info(f"Total de registros: {len(data)}")
    st.sidebar.info(f"Último update: {datetime.now().strftime('%H:%M:%S')}")
    
    # Auto refresh
    if auto_refresh:
        time.sleep(refresh_interval)
        st.rerun()

if __name__ == "__main__":
    main()
