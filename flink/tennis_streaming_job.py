#!/usr/bin/env python3

import json
import logging
from datetime import datetime
from typing import Tuple

from pyflink.common import WatermarkStrategy, Time, Row
from pyflink.common.serialization import SimpleStringSchema
from pyflink.datastream import StreamExecutionEnvironment
from pyflink.datastream.connectors.kafka import KafkaSource, KafkaOffsetsInitializer, KafkaSink, KafkaRecordSerializationSchema
from pyflink.datastream.window import TumblingProcessingTimeWindows
from pyflink.datastream.functions import MapFunction, AggregateFunction

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TennisBet:
    def __init__(self, timestamp: str, outcome: str, amount: float):
        self.timestamp = timestamp
        self.outcome = outcome
        self.amount = amount

    def __str__(self):
        return f"TennisBet(timestamp={self.timestamp}, outcome={self.outcome}, amount={self.amount})"


class BetAggregation:
    def __init__(self, outcome: str, count: int, total_amount: float, window_end: str):
        self.outcome = outcome
        self.count = count
        self.total_amount = total_amount
        self.window_end = window_end

    def __str__(self):
        return f"BetAggregation(outcome={self.outcome}, count={self.count}, total_amount={self.total_amount}, window_end={self.window_end})"


class JsonToBetMapper(MapFunction):
    def map(self, value: str) -> TennisBet:
        try:
            data = json.loads(value)
            return TennisBet(
                timestamp=data.get('timestamp', ''),
                outcome=data.get('outcome', ''),
                amount=float(data.get('amount', 0.0))
            )
        except Exception as e:
            logger.error(f"Error parsing JSON: {e}, value: {value}")
            return TennisBet('', 'unknown', 0.0)


class BetAggregator(AggregateFunction):
    def create_accumulator(self) -> Tuple[str, int, float]:
        return ('', 0, 0.0)

    def add(self, bet: TennisBet, accumulator: Tuple[str, int, float]) -> Tuple[str, int, float]:
        return (bet.outcome, accumulator[1] + 1, accumulator[2] + bet.amount)

    def get_result(self, accumulator: Tuple[str, int, float]) -> BetAggregation:
        window_end = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        return BetAggregation(
            outcome=accumulator[0],
            count=accumulator[1],
            total_amount=accumulator[2],
            window_end=window_end
        )

    def merge(self, acc1: Tuple[str, int, float], acc2: Tuple[str, int, float]) -> Tuple[str, int, float]:
        return (acc1[0], acc1[1] + acc2[1], acc1[2] + acc2[2])


class AggregationToCsvMapper(MapFunction):
    def map(self, aggregation: BetAggregation) -> Row:
        return Row(
            aggregation.window_end,
            aggregation.outcome,
            aggregation.count,
            round(aggregation.total_amount, 2)
        )


class AggregationToJsonMapper(MapFunction):
    def map(self, aggregation: BetAggregation) -> str:
        """Convert BetAggregation to JSON string for Kafka sink"""
        data = {
            'window_end': aggregation.window_end,
            'outcome': aggregation.outcome,
            'count': aggregation.count,
            'total_amount': round(aggregation.total_amount, 2),
            'timestamp': datetime.now().isoformat()
        }
        return json.dumps(data)


def main():
    # Set up the streaming execution environment
    env = StreamExecutionEnvironment.get_execution_environment()

    # Add Kafka connector JAR
    env.add_jars("file:///opt/flink/lib/flink-sql-connector-kafka-3.1.0-1.18.jar")

    # Disable checkpointing for this demo
    # env.enable_checkpointing(10000)

    # Set parallelism
    env.set_parallelism(1)

    logger.info("Starting Tennis Betting Stream Processing Job")

    # Configure Kafka source
    kafka_source = KafkaSource.builder() \
        .set_bootstrap_servers("kafka:29092") \
        .set_topics("tennis-bets") \
        .set_group_id("tennis-streaming-group") \
        .set_starting_offsets(KafkaOffsetsInitializer.earliest()) \
        .set_value_only_deserializer(SimpleStringSchema()) \
        .build()

    # Create data stream from Kafka
    kafka_stream = env.from_source(
        kafka_source,
        WatermarkStrategy.no_watermarks(),
        "Kafka Source"
    )

    # Parse JSON and extract bet information
    bet_stream = kafka_stream.map(JsonToBetMapper()).name("Parse JSON to TennisBet")

    # Aggregate by outcome in 10-second windows
    aggregated_stream = bet_stream \
        .key_by(lambda bet: bet.outcome) \
        .window(TumblingProcessingTimeWindows.of(Time.seconds(10))) \
        .aggregate(BetAggregator()).name("Aggregate Bets by Outcome")

    # Convert to CSV format and print for debugging
    csv_stream = aggregated_stream.map(AggregationToCsvMapper()).name("Convert to CSV Row")

    # Print results for debugging (keep for backward compatibility)
    csv_stream.print("Tennis Betting Aggregations")

    # Convert aggregations to JSON for Kafka sink
    json_stream = aggregated_stream.map(AggregationToJsonMapper()).name("Convert to JSON")

    # Configure Kafka sink for aggregations
    kafka_sink = KafkaSink.builder() \
        .set_bootstrap_servers("kafka:29092") \
        .set_record_serializer(
            KafkaRecordSerializationSchema.builder()
            .set_topic("tennis-aggregations")
            .set_value_serialization_schema(SimpleStringSchema())
            .build()
        ) \
        .build()

    # Send aggregations to Kafka
    json_stream.sink_to(kafka_sink).name("Kafka Aggregations Sink")

    # Execute the job
    logger.info("Executing Tennis Betting Stream Processing Job")
    env.execute("Tennis Betting Stream Processing")


if __name__ == "__main__":
    main()
