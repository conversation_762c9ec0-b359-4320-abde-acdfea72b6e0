#!/usr/bin/env python3

import json
import logging
from datetime import datetime

from pyflink.common import WatermarkStrategy, Time
from pyflink.common.serialization import SimpleStringSchema
from pyflink.datastream import StreamExecutionEnvironment
from pyflink.datastream.connectors.kafka import KafkaSource, KafkaOffsetsInitializer, KafkaSink, KafkaRecordSerializationSchema
from pyflink.datastream.functions import MapFunction

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestMessage:
    def __init__(self, message: str):
        self.message = message
        self.timestamp = datetime.now().isoformat()

    def __str__(self):
        return f"TestMessage(message={self.message}, timestamp={self.timestamp})"


class JsonToTestMapper(MapFunction):
    def map(self, value: str) -> TestMessage:
        try:
            data = json.loads(value)
            return TestMessage(f"Processed: {data.get('outcome', 'unknown')}")
        except Exception as e:
            logger.error(f"Error parsing JSON: {e}")
            return TestMessage("Error processing message")


class TestToJsonMapper(MapFunction):
    def map(self, test_msg: TestMessage) -> str:
        """Convert TestMessage to JSON string for Kafka sink"""
        data = {
            'message': test_msg.message,
            'timestamp': test_msg.timestamp,
            'processed_at': datetime.now().isoformat()
        }
        return json.dumps(data)


def main():
    # Set up the streaming execution environment
    env = StreamExecutionEnvironment.get_execution_environment()

    # Add Kafka connector JAR
    env.add_jars("file:///opt/flink/lib/flink-sql-connector-kafka-3.1.0-1.18.jar")

    # Set parallelism
    env.set_parallelism(1)

    logger.info("Starting Test Kafka Sink Job")

    # Configure Kafka source
    kafka_source = KafkaSource.builder() \
        .set_bootstrap_servers("kafka:29092") \
        .set_topics("tennis-bets") \
        .set_group_id("test-kafka-sink-group") \
        .set_starting_offsets(KafkaOffsetsInitializer.earliest()) \
        .set_value_only_deserializer(SimpleStringSchema()) \
        .build()

    # Create data stream from Kafka
    kafka_stream = env.from_source(
        kafka_source,
        WatermarkStrategy.no_watermarks(),
        "Kafka Source"
    )

    # Parse JSON and create test messages
    test_stream = kafka_stream.map(JsonToTestMapper()).name("Parse to Test Message")

    # Convert to JSON for Kafka sink
    json_stream = test_stream.map(TestToJsonMapper()).name("Convert to JSON")

    # Print for debugging
    json_stream.print("Test Messages")

    # Configure Kafka sink
    kafka_sink = KafkaSink.builder() \
        .set_bootstrap_servers("kafka:29092") \
        .set_record_serializer(
            KafkaRecordSerializationSchema.builder()
            .set_topic("tennis-aggregations")
            .set_value_serialization_schema(SimpleStringSchema())
            .build()
        ) \
        .build()

    # Send to Kafka
    json_stream.sink_to(kafka_sink).name("Test Kafka Sink")

    # Execute the job
    logger.info("Executing Test Kafka Sink Job")
    env.execute("Test Kafka Sink Job")


if __name__ == "__main__":
    main()
