#!/bin/bash

echo "🎾 Testing Complete Tennis Betting Pipeline with Kafka"
echo "=" * 60

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS") echo -e "${GREEN}✅ $message${NC}" ;;
        "ERROR") echo -e "${RED}❌ $message${NC}" ;;
        "WARNING") echo -e "${YELLOW}⚠️ $message${NC}" ;;
        "INFO") echo -e "${BLUE}ℹ️ $message${NC}" ;;
    esac
}

# Step 1: Stop existing containers
print_status "INFO" "Stopping existing containers..."
docker-compose down

# Step 2: Build and start services
print_status "INFO" "Building and starting services..."
if docker-compose up --build -d; then
    print_status "SUCCESS" "Services started successfully"
else
    print_status "ERROR" "Failed to start services"
    exit 1
fi

# Step 3: Wait for services to be ready
print_status "INFO" "Waiting for services to be ready..."
sleep 30

# Step 4: Check if all containers are running
print_status "INFO" "Checking container status..."
containers=("zookeeper" "kafka" "kafka-setup" "flink-jobmanager" "flink-taskmanager" "tennis-simulator" "tennis-dashboard")

for container in "${containers[@]}"; do
    if docker ps --format "table {{.Names}}" | grep -q "$container"; then
        print_status "SUCCESS" "$container is running"
    else
        print_status "ERROR" "$container is not running"
    fi
done

# Step 5: Test Kafka topics
print_status "INFO" "Testing Kafka topics..."
if ./scripts/test-kafka-topics.sh; then
    print_status "SUCCESS" "Kafka topics test passed"
else
    print_status "WARNING" "Kafka topics test had issues"
fi

# Step 6: Check Flink job status
print_status "INFO" "Checking Flink job status..."
sleep 10
if curl -s http://localhost:8081/jobs | grep -q "RUNNING"; then
    print_status "SUCCESS" "Flink job is running"
else
    print_status "WARNING" "Flink job may not be running yet"
fi

# Step 7: Test data flow
print_status "INFO" "Testing data flow (30 seconds)..."
echo "Checking for messages in tennis-aggregations topic..."
timeout 30s docker exec kafka kafka-console-consumer \
    --bootstrap-server localhost:29092 \
    --topic tennis-aggregations \
    --from-beginning \
    --max-messages 5 || print_status "WARNING" "No messages found or timeout reached"

# Step 8: Check dashboard accessibility
print_status "INFO" "Checking dashboard accessibility..."
if curl -s http://localhost:8501 > /dev/null; then
    print_status "SUCCESS" "Dashboard is accessible at http://localhost:8501"
else
    print_status "ERROR" "Dashboard is not accessible"
fi

# Step 9: Show service URLs
echo ""
print_status "INFO" "Service URLs:"
echo "  🎾 Dashboard: http://localhost:8501"
echo "  🔧 Flink UI: http://localhost:8081"
echo "  📊 Kafka Topics:"
echo "    - tennis-bets (input)"
echo "    - tennis-aggregations (output)"

# Step 10: Show useful commands
echo ""
print_status "INFO" "Useful commands:"
echo "  📋 View logs: docker-compose logs -f [service-name]"
echo "  🔍 Test Kafka: ./scripts/test-kafka-topics.sh"
echo "  🐍 Test Consumer: python scripts/test-kafka-consumer.py"
echo "  🛑 Stop all: docker-compose down"

echo ""
print_status "SUCCESS" "Pipeline test completed! Check the URLs above to verify everything is working."
