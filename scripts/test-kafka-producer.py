#!/usr/bin/env python3

import json
import time
from datetime import datetime
from kafka import KafkaProducer

def test_kafka_producer():
    """Test sending messages to tennis-aggregations topic"""
    
    print("🔍 Testing Kafka Producer for tennis-aggregations topic...")
    
    try:
        producer = KafkaProducer(
            bootstrap_servers=['localhost:9092'],
            value_serializer=lambda x: json.dumps(x).encode('utf-8')
        )
        
        print("✅ Connected to Kafka")
        
        # Send test messages
        for i in range(5):
            test_message = {
                'window_end': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'outcome': f'test_outcome_{i}',
                'count': i + 1,
                'total_amount': (i + 1) * 100.0,
                'timestamp': datetime.now().isoformat()
            }
            
            future = producer.send('tennis-aggregations', test_message)
            result = future.get(timeout=10)
            
            print(f"📨 Sent message {i+1}: {test_message}")
            time.sleep(1)
        
        producer.close()
        print("✅ All test messages sent successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_kafka_producer()
