#!/bin/bash

echo "🔍 Testing Kafka Topics Setup..."

# Check if Kafka container is running
if ! docker ps | grep -q kafka; then
    echo "❌ Kafka container is not running. Please start with: docker-compose up -d kafka"
    exit 1
fi

echo "✅ Kafka container is running"

# List all topics
echo "📋 Listing all Kafka topics:"
docker exec kafka kafka-topics --bootstrap-server localhost:29092 --list

# Check if tennis-bets topic exists
echo "🎾 Checking tennis-bets topic:"
if docker exec kafka kafka-topics --bootstrap-server localhost:29092 --describe --topic tennis-bets; then
    echo "✅ tennis-bets topic exists"
else
    echo "❌ tennis-bets topic does not exist"
fi

# Check if tennis-aggregations topic exists
echo "📊 Checking tennis-aggregations topic:"
if docker exec kafka kafka-topics --bootstrap-server localhost:29092 --describe --topic tennis-aggregations; then
    echo "✅ tennis-aggregations topic exists"
else
    echo "❌ tennis-aggregations topic does not exist"
fi

# Test consuming from tennis-aggregations topic (non-blocking)
echo "🔍 Testing consumption from tennis-aggregations topic (5 seconds):"
timeout 5s docker exec kafka kafka-console-consumer --bootstrap-server localhost:29092 --topic tennis-aggregations --from-beginning || echo "No messages found or timeout reached"

echo "✅ Kafka topics test completed!"
