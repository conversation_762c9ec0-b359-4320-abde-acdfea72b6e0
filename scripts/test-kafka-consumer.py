#!/usr/bin/env python3

import json
import time
from datetime import datetime
from kafka import KafkaConsumer
from kafka.errors import KafkaError

def test_kafka_consumer():
    """Test Kafka consumer for tennis-aggregations topic"""
    
    print("🔍 Testing Kafka Consumer for tennis-aggregations topic...")
    
    # Try different bootstrap servers
    servers = ['localhost:9092', 'kafka:29092']
    
    for server in servers:
        try:
            print(f"🔗 Attempting to connect to {server}...")
            
            consumer = KafkaConsumer(
                'tennis-aggregations',
                bootstrap_servers=[server],
                auto_offset_reset='earliest',
                enable_auto_commit=True,
                group_id='test-consumer-group',
                value_deserializer=lambda x: x.decode('utf-8'),
                consumer_timeout_ms=5000  # 5 second timeout
            )
            
            print(f"✅ Connected to Kafka at {server}")
            print("📡 Listening for messages (5 seconds)...")
            
            message_count = 0
            start_time = time.time()
            
            for message in consumer:
                try:
                    data = json.loads(message.value)
                    print(f"📨 Received message: {data}")
                    message_count += 1
                    
                    if time.time() - start_time > 5:  # Stop after 5 seconds
                        break
                        
                except json.JSONDecodeError as e:
                    print(f"❌ Error parsing JSON: {e}")
                except Exception as e:
                    print(f"❌ Error processing message: {e}")
            
            consumer.close()
            
            if message_count > 0:
                print(f"✅ Successfully received {message_count} messages")
            else:
                print("ℹ️ No messages received (this is normal if no data is being produced)")
            
            return True
            
        except KafkaError as e:
            print(f"❌ Kafka error with {server}: {e}")
        except Exception as e:
            print(f"❌ Connection error with {server}: {e}")
    
    print("❌ Could not connect to any Kafka server")
    return False

def test_kafka_producer():
    """Test sending a sample message to tennis-aggregations topic"""
    
    print("\n🔍 Testing Kafka Producer for tennis-aggregations topic...")
    
    try:
        from kafka import KafkaProducer
        
        producer = KafkaProducer(
            bootstrap_servers=['localhost:9092'],
            value_serializer=lambda x: json.dumps(x).encode('utf-8')
        )
        
        # Send a test message
        test_message = {
            'window_end': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'outcome': 'test_outcome',
            'count': 1,
            'total_amount': 10.0,
            'timestamp': datetime.now().isoformat()
        }
        
        future = producer.send('tennis-aggregations', test_message)
        result = future.get(timeout=10)
        
        print(f"✅ Test message sent successfully: {test_message}")
        producer.close()
        return True
        
    except Exception as e:
        print(f"❌ Error sending test message: {e}")
        return False

if __name__ == "__main__":
    print("🎾 Tennis Betting Kafka Test")
    print("=" * 50)
    
    # Test consumer
    consumer_success = test_kafka_consumer()
    
    # Test producer
    producer_success = test_kafka_producer()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"Consumer: {'✅ PASS' if consumer_success else '❌ FAIL'}")
    print(f"Producer: {'✅ PASS' if producer_success else '❌ FAIL'}")
    
    if consumer_success and producer_success:
        print("🎉 All tests passed! Kafka setup is working correctly.")
    else:
        print("⚠️ Some tests failed. Check Kafka configuration.")
